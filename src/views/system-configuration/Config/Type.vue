<script setup lang="ts">
import {
  LeftType,
  type selectType,
  TypeOrPropertyType
} from '@/views/system-configuration/Config/help'
import Left from '@/views/system-configuration/components/Left.vue'
import Layout from '@/views/system-configuration/components/Type/Layout.vue'
import ContraitTemplate from '@/views/system-configuration/components/ContraitTemplate.vue'
import { LayoutProperty } from '@/api/systemConfiguration/type'
import { LayoutTable } from '@/views/system-configuration/components/Type/help'
import { getDictByCodeApi } from '@/api/common'
const selectedType = ref<selectType | null>(null)
const activeName = ref('property')
watch(
  () => selectedType.value,
  () => {
    activeName.value = 'property'
  }
)
const rightPanal = reactive({
  visible: false,
  row: {}
})
const tableDetail = ref<LayoutProperty.RelationDetailResp[]>()
const openDetail = (row: LayoutProperty.RelationDetailResp) => {
  rightPanal.visible = true
  rightPanal.row = row
}
const getLayoutData = async () => {
  if (!selectedType.value?.name) {
    return false
  }
  const result = await getDictByCodeApi({ dictCode: `ENUMERATED_${selectedType.value?.name}` })
  tableDetail.value = result?.data || []
  if (tableDetail.value.length == 0) {
    rightPanal.visible = false
  }
  result?.data?.[0] && openDetail(result.data[0])
}
watch(
  () => activeName.value,
  () => {
    if (activeName.value === 'layout') {
      getLayoutData()
    }
  },
  {
    deep: true
  }
)
</script>

<template>
  <ContentWrap>
    <ElRow :gutter="20">
      <ElCol :span="5">
        <Left v-model:selected="selectedType" :type="LeftType.type" />
      </ElCol>
      <ElCol :span="19">
        <p class="title">{{ selectedType?.typeShowName }}</p>
        <el-tabs v-model="activeName" style="height: calc(100% - 30px)">
          <el-tab-pane label="属性" name="property">
            <ContraitTemplate
              :constraintAttribute="TypeOrPropertyType.property"
              :constraintId="selectedType?.id"
            />
          </el-tab-pane>
          <el-tab-pane v-if="Number(selectedType?.value) > 1" label="布局" name="layout">
            <ElRow :gutter="20" style="height: 100%">
              <ElCol :span="8">
                <Table :columns="LayoutTable" :data="tableDetail">
                  <template #operation="{ row }">
                    <ElButton type="primary" link @click="openDetail(row)">详情</ElButton>
                  </template>
                </Table>
              </ElCol>
              <ElCol v-if="rightPanal.visible" :span="16">
                <Layout :selectedType="selectedType" :selectedRow="rightPanal.row" />
              </ElCol>
            </ElRow>
          </el-tab-pane>
        </el-tabs>
      </ElCol>
    </ElRow>
  </ContentWrap>
</template>

<style scoped lang="less">
.el-tab-pane {
  height: 100%;
}

:deep(.enum-types-panel) {
  border-right: 1px solid var(--el-border-color-light);
}

.title {
  padding: 10px 0;
  font-size: 18px;
  font-weight: bold;
}
</style>
