<script setup lang="ts">
import {
  constraintTypeMap,
  TypeConfig,
  TypeOrPropertyType
} from '@/views/system-configuration/Config/help'
import { scrollProp } from '@/plugins/vxeTable'
import { VxeGridInstance, VxeGridProps } from 'vxe-table'
import {
  deletePropertyRelation,
  deleteTypeRelation,
  getRelationDetail,
  getTypeRelationDetail
} from '@/api/systemConfiguration/type'
import { useTableHeight } from '@/hooks/web/useTableHeight'
import { TypeApi } from '@/api/systemConfiguration/type'
import AddPropertyDialog from '@/views/system-configuration/components/Type/AddPropertyDialog.vue'
import constraint from '@/views/system-configuration/components/Type/constraint.vue'
const typeTableRef = ref<VxeGridInstance>()
const maxHeight = useTableHeight({ tableRef: typeTableRef })
const loading = ref(false)
const removeLoading = ref(false)
const contraintList = ref<TypeApi.PlmBaseConstraintInstanceResp[]>([])
const tableOptions = computed(
  () =>
    ({
      border: true,
      showOverflow: true,
      height: maxHeight.value - 90,
      loading: loading.value,
      data: contraintList.value,
      radioConfig: {
        checkMethod({ row }) {
          return !row.isExtends
        }
      },
      columns: TypeConfig.TypeTableConfig,
      ...scrollProp
    } as VxeGridProps)
)
const addDialog = reactive({
  visible: false,
  data: {}
})
defineOptions({
  name: 'ContraitTemplate'
})
const props = defineProps({
  constraintAttribute: {
    type: String,
    default: ''
  },
  constraintId: {
    type: String,
    default: ''
  }
})
const fetachRelationProperty = async () => {
  if (!props.constraintId) {
    return false
  }
  loading.value = true
  const [error, result] =
    props.constraintAttribute == TypeOrPropertyType.type
      ? await getRelationDetail(props.constraintId)
      : await getTypeRelationDetail(props.constraintId)
  if (error === null && result.data) {
    contraintList.value = result.data || []
    if (contraintList.value[0]) {
      openDetail(contraintList.value[0])
    } else {
      constraintDialog.visible = false
    }
  }
  loading.value = false
}
const addPropertyDialog = () => {
  addDialog.visible = true
}
//删除
const removeProperty = () => {
  const row = typeTableRef.value?.getRadioRecord()
  if (!row) {
    ElMessage.error('请选择一条数据')
    return
  }
  if (row.isExtends) {
    ElMessage.error('扩展属性不能删除')
    return
  }
  ElMessageBox.confirm('确定要删除该属性的依赖关系？删除后将无法恢复', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    removeLoading.value = true
    props.constraintAttribute == TypeOrPropertyType.type
      ? await deletePropertyRelation({
          relationId: row.relationId,
          businessId: props.constraintId
        })
      : await deleteTypeRelation({
          relationId: row.relationId,
          businessId: props.constraintId
        })
    ElMessage.success('删除成功')
    removeLoading.value = false
    fetachRelationProperty()
  })
}
const constraintDialog = reactive({
  visible: false,
  id: 0,
  type: '',
  name: '',
  isExtends: false, //是否是继承属性
  defaultProperty: false,
  relationId: '',
  reload: false
})
//约束
const openDetail = (row: TypeApi.PlmBaseConstraintInstanceResp) => {
  constraintDialog.visible = true
  constraintDialog.reload = !constraintDialog.reload
  constraintDialog.id = row.propertyId || 0
  constraintDialog.type = constraintTypeMap?.[row.type] || ''
  constraintDialog.name = row.name
  constraintDialog.isExtends = row.isExtends
  constraintDialog.defaultProperty = row.defaultProperty
  constraintDialog.relationId = row.relationId
}
watch(
  () => props.constraintId,
  async () => {
    await fetachRelationProperty()
  },
  {
    immediate: true,
    deep: true
  }
)
</script>
<template>
  <ContentWrap>
    <ElRow :gutter="20">
      <ElCol :span="constraintDialog.visible ? 16 : 24">
        <div class="operation">
          <ElButton @click="addPropertyDialog" type="primary"
            ><Icon icon="ep:plus" />添加属性</ElButton
          >
          <ElButton @click="removeProperty" :loading="removeLoading" type="primary"
            ><Icon icon="ep:delete" />移除</ElButton
          >
        </div>
        <VxeGrid ref="typeTableRef" v-bind="tableOptions">
          <template #isExtends="{ row }">
            <ElTag v-if="row.isExtends" type="success">是</ElTag>
            <span v-else>否</span>
          </template>
          <template #operation="{ row }">
            <ElButton link type="primary" @click="openDetail(row)">详情</ElButton>
          </template>
        </VxeGrid>
      </ElCol>
      <ElCol :span="8">
        <!--如果是扩展属性不能进行编辑-->
        <constraint
          v-if="constraintDialog.visible"
          :constraint-attribute-id="constraintDialog.id"
          :constraintType="constraintDialog.type"
          :constraintAttribute="constraintAttribute"
          :name="constraintDialog.name"
          :defaultProperty="constraintDialog.defaultProperty"
          :isExtends="constraintDialog.isExtends"
          :relationId="constraintDialog.relationId"
          v-model:visible="constraintDialog.reload"
        />
      </ElCol>
    </ElRow>
  </ContentWrap>
  <AddPropertyDialog
    :constraintAttribute="constraintAttribute"
    :constraintId="constraintId"
    v-model="addDialog.visible"
    @update-success="fetachRelationProperty"
  />
</template>
<style scoped lang="less">
.operation {
  margin-bottom: 10px;
}
</style>
