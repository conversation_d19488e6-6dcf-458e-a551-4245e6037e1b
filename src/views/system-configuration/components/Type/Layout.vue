<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElRow, ElCol, ElButton, ElInput, ElIcon } from 'element-plus'
import draggable from 'vuedraggable'
import { Plus, Delete, Edit } from '@element-plus/icons-vue'
import DraggablePanel from './DraggablePanel.vue'
import { Icon } from '@/components/Icon'
import {
  getPage,
  getPageAttributeList,
  getPageCategoryList,
  LayoutProperty,
  savePageAttribute,
  TypeApi
} from '@/api/systemConfiguration/type'
import type { selectType } from '@/views/system-configuration/Config/help'
const props = defineProps<{
  selectedType: selectType | null
  selectedRow: LayoutProperty.RelationDetailResp
}>()

// 左侧可拖动的属性列表
const typeProperties = ref<TypeApi.RelationDetailResp[]>([])
// 左侧分类属性列表 (示例数据，请根据实际情况修改)
const categoryProperties = ref<TypeApi.RelationDetailResp[]>([])
// 页面加载数据
const loading = ref(false)
//放大
const isFullscreen = ref(false)

// 跟踪已使用的属性

const usedProperties = ref(new Set())
// 表单数据对象
const formData = reactive({})

// 栅格系统配置
const gridConfig = reactive({
  columns: 24, // 总列数改为24
  gutter: 12, // 列间距
  defaultSpan: 6 // 默认每个组件占6列
})

// 滚动区域引用
const materialTargetRef = ref()
const layoutContainerRef = ref()

// Tab相关数据
const tabs = ref([
  {
    id: 1,
    name: '基本信息',
    isEditing: false,
    gridItems: [[]] // 每个tab有自己的网格数据
  }
])

const activeTabId = ref(1)

// 更新已使用属性的集合
const updateUsedProperties = () => {
  const used = new Set()
  tabs.value.forEach((tab) => {
    tab.gridItems.forEach((row) => {
      row.forEach((item: { value: string }) => {
        if (item.value) {
          used.add(item.value)
        }
      })
    })
  })
  usedProperties.value = used
}

// 当属性被删除时，从已使用集合中移除
const handleElementRemoved = ({ element }) => {
  if (element && element.value) {
    usedProperties.value.delete(element.value)
  }
}

// 计算每列的宽度百分比
const columnWidth = computed(() => {
  return `calc((100% - ${(gridConfig.columns - 1) * gridConfig.gutter}px) / ${gridConfig.columns})`
})

// 添加新Tab
const addTab = () => {
  const newId = tabs.value.length > 0 ? Math.max(...tabs.value.map((t) => t.id)) + 1 : 1
  tabs.value.push({
    id: newId,
    name: `新标签${newId}`,
    isEditing: false,
    gridItems: [[]]
  })
  activeTabId.value = newId
}

// 删除Tab
const removeTab = (tabId) => {
  const index = tabs.value.findIndex((t) => t.id === tabId)
  if (index === -1) return

  // 如果删除的是当前激活的tab，则激活前一个或后一个tab
  if (activeTabId.value === tabId) {
    if (index > 0) {
      activeTabId.value = tabs.value[index - 1].id
    } else if (tabs.value.length > 1) {
      activeTabId.value = tabs.value[1].id
    }
  }

  tabs.value.splice(index, 1)
}

// 编辑Tab名称
const startEditingTab = (tab) => {
  tab.isEditing = true
  nextTick(() => {
    const input = document.getElementById(`tab-name-input-${tab.id}`)
    if (input) {
      input.focus()
    }
  })
}

const finishEditingTab = (tab) => {
  tab.isEditing = false
  // 确保tab名称不为空
  if (!tab.name.trim()) {
    tab.name = `标签${tab.id}`
  }
}
// 处理添加行后的滚动
const handleAddRow = () => {
  nextTick(() => {
    if (materialTargetRef.value) {
      materialTargetRef.value.scrollTop = materialTargetRef.value.scrollHeight
    }
  })
}

// 更新表单数据
const updateFormData = (newFormData) => {
  Object.assign(formData, newFormData)
}
//保存布局
const saveLayout = async () => {
  console.log(tabs.value)
  if (!props.selectedRow?.value || !props.selectedType?.id) {
    return false
  }
  loading.value = true
  const [error, result] = await savePageAttribute({
    pageLayoutCode: props.selectedRow?.value,
    pageTypeCategoryId: props.selectedType?.id,
    pageLayoutName: props.selectedRow?.label,
    pageTypeCategoryName: props.selectedType?.name,
    pageAttributes: JSON.stringify(tabs.value)
  })
  if (error === null && result) {
    ElMessage.success('保存成功')
  }
  loading.value = false
}
//获取类型属性和分类属性
const getTypeProperty = async () => {
  if (!props.selectedType?.id) {
    return false
  }
  loading.value = true
  const [error, result] = await getPageAttributeList({ typeCategoryId: props.selectedType?.id })
  if (error === null && result) {
    typeProperties.value = result.data || []
    console.log('类型属性数据:', typeProperties.value)
  }
  const [error1, result1] = await getPageCategoryList({ typeCategoryId: props.selectedType?.id })
  if (error1 === null && result1) {
    categoryProperties.value = result1.data || []
    console.log('分类属性数据:', categoryProperties.value)
  }
  loading.value = false
}

//获取布局
const getLayout = async () => {
  if (!props.selectedRow?.value || !props.selectedType?.id) {
    return false
  }
  loading.value = true
  const [error, result] = await getPage({
    pageLayoutCode: props.selectedRow?.value,
    pageTypeCategoryId: props.selectedType?.id
  })
  if (error === null && result.data) {
    tabs.value = JSON.parse(result.data?.pageAttributes) || []
  } else {
    tabs.value = [
      {
        id: 1,
        name: '基本信息',
        isEditing: false,
        gridItems: [[]] // 每个tab有自己的网格数据
      }
    ]
  }
  loading.value = false
}
// 在组件挂载时初始化已使用属性
onMounted(() => {
  getTypeProperty()
  getLayout()
  updateUsedProperties()

  // 添加测试数据
  if (typeProperties.value.length === 0) {
    typeProperties.value = [
      { value: 'test1', name: '测试属性1', type: 'STRING' },
      { value: 'test2', name: '测试属性2', type: 'INT' }
    ]
    console.log('添加了测试数据:', typeProperties.value)
  }
})
// 监听窗口大小变化，调整滚动区域高度
onMounted(() => {
  const updateScrollHeight = () => {
    if (materialTargetRef.value) {
      const windowHeight = window.innerHeight
      const offsetTop = materialTargetRef.value.getBoundingClientRect().top
      const padding = 20 // 底部留出一些空间
      materialTargetRef.value.style.maxHeight = `${windowHeight - offsetTop - padding}px`
    }
  }

  window.addEventListener('resize', updateScrollHeight)
  updateScrollHeight() // 初始化时调用一次

  // 组件卸载时移除事件监听
  onUnmounted(() => {
    window.removeEventListener('resize', updateScrollHeight)
  })
})

// 监听表单数据变化，更新已使用属性
watch(
  () => tabs.value,
  () => {
    updateUsedProperties()
  },
  { deep: true }
)
watch(
  () => props.selectedRow,
  () => {
    getTypeProperty()
    getLayout()
    updateUsedProperties()
  },
  { deep: true, immediate: true }
)
</script>

<template>
  <div
    v-loading="loading"
    :class="'layout-container ' + (isFullscreen ? 'fullscreen' : '')"
    ref="layoutContainerRef"
  >
    <ElRow style="height: calc(100% - 60px)">
      <!-- 左侧属性列表 -->
      <ElCol :span="isFullscreen ? 4 : 6" style="height: 100%">
        <div class="material-source">
          <!-- 类型属性 -->
          <div class="property-section">
            <p>类型属性</p>
            <!--            <div class="span-controls">-->
            <!--              <span>默认列宽：</span>-->
            <!--              <ElSelect v-model="gridConfig.defaultSpan" size="small" style="width: 120px">-->
            <!--                <ElOption v-for="i in gridConfig.columns" :key="i" :value="i" :label="`${i}列`" />-->
            <!--              </ElSelect>-->
            <!--            </div>-->
            <!-- 类型属性拖动区域 -->
            <draggable
              v-model="typeProperties"
              group="materials"
              item-key="value"
              class="drop-area source"
              :clone="
                (original) => {
                  return {
                    id: Date.now(),
                    ...JSON.parse(JSON.stringify(original))
                  }
                }
              "
              @start="(event) => console.log('类型属性拖拽开始:', event)"
              @end="(event) => console.log('类型属性拖拽结束:', event)"
            >
              <template #item="{ element }">
                <div class="material-item" v-if="!usedProperties.has(element.value)">
                  {{ element.name }}
                </div>
              </template>
            </draggable>
          </div>

          <!-- 分类属性 -->
          <div class="property-section">
            <p>分类属性</p>
            <!-- 分类属性拖动区域 -->
            <!--            <draggable-->
            <!--              v-model="categoryProperties"-->
            <!--              :group="{ name: 'materials', pull: 'clone', put: false }"-->
            <!--              item-key="value"-->
            <!--              class="drop-area"-->
            <!--              :clone="(original) => JSON.parse(JSON.stringify(original))"-->
            <!--              @start="(event) => console.log('分类属性拖拽开始:', event)"-->
            <!--              @end="(event) => console.log('分类属性拖拽结束:', event)"-->
            <!--            >-->
            <!--              <template #item="{ element }">-->
            <!--                <div class="material-item" v-if="!usedProperties.has(element.value)">-->
            <!--                  {{ element.name }}-->
            <!--                </div>-->
            <!--              </template>-->
            <!--            </draggable>-->
          </div>
        </div>
      </ElCol>

      <!-- 右侧表单区域 -->
      <ElCol
        :span="isFullscreen ? 20 : 18"
        style="display: flex; height: 100%; flex-direction: column"
      >
        <div class="right-panel">
          <!-- Tab标签区域 -->
          <div class="tabs-header">
            <div
              v-for="tab in tabs"
              :key="tab.id"
              :class="['tab-item', { active: tab.id === activeTabId }]"
              @click="activeTabId = tab.id"
            >
              <!-- Tab名称（可编辑） -->
              <div v-if="!tab.isEditing" class="tab-name">
                {{ tab.name }}
                <ElIcon class="edit-icon" @click.stop="startEditingTab(tab)">
                  <Edit />
                </ElIcon>
              </div>
              <ElInput
                v-else
                :id="`tab-name-input-${tab.id}`"
                v-model="tab.name"
                size="small"
                @blur="finishEditingTab(tab)"
                @keyup.enter="finishEditingTab(tab)"
                @click.stop
              />

              <!-- 删除按钮 (不允许删除最后一个tab) -->
              <ElIcon v-if="tabs.length > 1" class="close-icon" @click.stop="removeTab(tab.id)">
                <Delete />
              </ElIcon>
            </div>

            <!-- 添加新Tab按钮 -->
            <div class="add-tab" @click="addTab">
              <ElIcon><Plus /></ElIcon>
            </div>
            <!--放大缩小--->
            <Icon
              class="exit-fullscreen"
              :size="18"
              @click="isFullscreen = !isFullscreen"
              :icon="isFullscreen ? 'zmdi:fullscreen-exit' : 'zmdi:fullscreen'"
            />
          </div>

          <!-- Tab内容区域 -->
          <div class="tabs-content" ref="materialTargetRef">
            <div v-for="tab in tabs" :key="tab.id" v-show="tab.id === activeTabId" class="tab-pane">
              <DraggablePanel
                v-model="tab.gridItems"
                :grid-config="gridConfig"
                :form-data="formData"
                :column-width="columnWidth"
                @add-row="handleAddRow"
                @remove-element="handleElementRemoved"
                @update:form-data="updateFormData"
              />
            </div>
          </div>
        </div>
      </ElCol>
    </ElRow>
    <div class="flex justify-center">
      <ElButton class="mt-2" type="primary" @click="saveLayout">保存</ElButton>
    </div>
  </div>
</template>
<style scoped lang="less">
.layout-container {
  height: 100%;
}

.material-source {
  height: 100%;
  padding: 10px;
  overflow-y: auto;
  background-color: #f0f2f5;
  border-radius: 8px;

  .property-section {
    margin-bottom: 20px;

    > p {
      margin-bottom: 10px;
      font-size: 14px;
      font-weight: bold;
    }
  }

  .span-controls {
    display: flex;
    margin-bottom: 10px;
    align-items: center;
    gap: 8px;
  }

  .drop-area {
    min-height: 50px;
  }
}

.right-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f8f9fa;
  border: 2px dashed #ccc;
  border-radius: 8px;
}

.tabs-header {
  display: flex;
  padding: 8px 8px 0 8px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #ddd;
  flex-wrap: wrap;

  .tab-item {
    display: flex;
    padding: 8px 15px;
    margin-right: 5px;
    margin-bottom: -1px;
    cursor: pointer;
    background-color: #f0f0f0;
    border: 1px solid transparent;
    border-radius: 4px 4px 0 0;
    align-items: center;

    &.active {
      background-color: white;
      border-color: #ddd;
      border-bottom-color: white;
    }

    .tab-name {
      display: flex;
      align-items: center;

      .edit-icon {
        margin-left: 5px;
        font-size: 14px;
        opacity: 0.5;

        &:hover {
          opacity: 1;
        }
      }
    }

    .close-icon {
      margin-left: 8px;
      font-size: 14px;
      opacity: 0.5;

      &:hover {
        color: #f56c6c;
        opacity: 1;
      }
    }

    .el-input {
      width: 120px;
    }
  }

  .add-tab {
    display: flex;
    padding: 8px 10px;
    color: #409eff;
    cursor: pointer;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: #f0f0f0;
      border-radius: 4px;
    }
  }

  .exit-fullscreen {
    position: absolute;
    top: 19px;
    right: 20px;
    font-size: 18px;
    color: black;
    cursor: pointer;
  }
}

.tabs-content {
  padding: 15px;
  overflow-y: auto;
  flex: 1;

  /* 设置滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #ccc transparent;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 3px;
  }

  .tab-pane {
    height: 100%;
  }
}

.material-item {
  padding: 4px 10px;
  margin: 4px 0;
  font-size: 12px;
  cursor: move;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 拖拽时的视觉效果 */
.sortable-chosen {
  background-color: #ebf5fb !important;
  opacity: 0.8;
}

.sortable-ghost {
  background: #c8ebfb !important;
  opacity: 0.5;
}

.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10;
  width: 100%;
  height: 100%;
}
</style>
