<script setup lang="ts">
import draggable from 'vuedraggable'
import { Plus, Delete } from '@element-plus/icons-vue'
import { componentRap, constArr, constraintTypeMap } from '../../Config/help'
import { ElSelect } from 'element-plus'
import { getDictByCodeApi } from '@/api/common'
import { ApiSelect } from '@/components/ApiSelect'

const props = defineProps({
  // 拖拽区域的数据
  modelValue: {
    type: Array,
    required: true
  },
  // 栅格配置
  gridConfig: {
    type: Object,
    required: true
  },
  // 表单数据
  formData: {
    type: Object,
    required: true
  },
  // 列宽计算函数
  columnWidth: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'add-row', 'remove-element', 'update:formData'])

// 当有新元素添加到右侧时
const onAdd = (event, rowIndex) => {
  const element: any = props.modelValue[rowIndex]
  // 初始化表单数据 - 使用emit更新formData而不是直接修改
  const updatedFormData = { ...props.formData, [element.value]: '' }
  emit('update:formData', updatedFormData)

  // 设置默认列数
  if (!element.span) {
    element.span = props.gridConfig.defaultSpan
  }

  // 设置默认可见性
  if (!element.visibility) {
    element.visibility = 'readWrite' // 默认为读/写
  }

  // 如果是拖到空行，可能需要特殊处理
  if (!props.modelValue[rowIndex]) {
    const newValue = [...props.modelValue]
    newValue[rowIndex] = []
    emit('update:modelValue', newValue)
  }

  // 计算当前行已占用的列数
  const usedColumns = props.modelValue[rowIndex].reduce((sum, item) => sum + (item.span || 1), 0)

  // 如果添加新元素后超出总列数，自动调整为剩余可用列数
  if (usedColumns + element.span > props.gridConfig.columns) {
    element.span = Math.max(1, props.gridConfig.columns - usedColumns)
  }
}

// 根据约束条件获取表单项属性
const getFormItemProps = (element) => {
  if (!element.constraintValue) return {}
  const constraint = JSON.parse(element.constraintValue)
  const props: Record<string, any> = {}
  const needArr = ['disabled', 'placeholder', 'clearable', 'imageSize']
  needArr.forEach((item) => {
    props[item] = constraint[item]?.value || ''
  })
  // 图片需要处理啊
  if (constraintTypeMap[element.type] == constraintTypeMap.ATTACHMENT) {
    props.listType = 'picture-card'
    props.autoUpload = 'false'
  }
  // teatArea需要处理
  if (constraintTypeMap[element.type] == constraintTypeMap.RICH_TEXT) {
    props.type = 'textarea'
    props.autoSize = `{ minRows:${constraint['rows']?.value} }`
  }
  if (element.visibility === 'readOnly') {
    // 根据可见性设置禁用状态
    props.disabled = true
  }
  return props
}
// 计算每行的总列数，用于检查是否超出限制
const getRowColumns = (row) => {
  return row.reduce((sum, item) => sum + (item.span || 1), 0)
}

// 调整元素的列宽
const adjustElementSpan = (element, newSpan, rowIndex) => {
  const row = props.modelValue[rowIndex]
  const currentSpan = element.span || 1
  const otherColumnsUsed = getRowColumns(row) - currentSpan

  // 确保不超过行的总列数
  const maxAllowedSpan = props.gridConfig.columns - otherColumnsUsed
  element.span = Math.min(newSpan, maxAllowedSpan)
}

// 更新元素的可见性
const updateElementVisibility = (element, visibility) => {
  element.visibility = visibility
}

// 删除元素
const removeElement = (rowIndex, elementIndex) => {
  const element = props.modelValue[rowIndex][elementIndex]
  const newValue = [...props.modelValue]
  newValue[rowIndex].splice(elementIndex, 1)

  // 如果行为空且不是唯一的行，则删除该行
  if (newValue[rowIndex].length === 0 && newValue.length > 1) {
    newValue.splice(rowIndex, 1)
  }
  emit('update:modelValue', newValue)

  // 从表单数据中删除 - 使用emit更新formData而不是直接修改
  if (element && element.value) {
    const updatedFormData = { ...props.formData }
    delete updatedFormData[element.value]
    emit('update:formData', updatedFormData)
  }

  // 通知父组件元素被删除，以便更新左侧可用属性
  emit('remove-element', { rowIndex, elementIndex, element })
}

// 添加新行
const addRow = () => {
  const newValue = [...props.modelValue, []]
  emit('update:modelValue', newValue)
  emit('add-row')
}
</script>

<template>
  <div class="draggable-panel">
    <ElForm label-position="left" :model="formData">
      <!-- 网格布局区域 -->
      <div class="grid-layout">
        <div class="grid-container">
          <!-- 每一行 -->
          <div v-for="(row, rowIndex) in modelValue" :key="rowIndex" class="grid-row">
            <!-- 拖拽区域 -->
            <draggable
              :model-value="row"
              @update:model-value="
                (val) => {
                  const newValue = [...modelValue]
                  newValue[rowIndex] = val
                  $emit('update:modelValue', newValue)
                }
              "
              group="materials"
              item-key="value"
              @add="(event) => onAdd(event, rowIndex)"
              class="drop-area target"
            >
              <template #item="{ element, index }">
                <div
                  class="grid-item"
                  :style="{
                    width: `calc(${element.span || 6} * ${columnWidth} + ${
                      (element.span || 6) - 1.5
                    } * ${gridConfig.gutter}px)`,
                    marginRight: `${gridConfig.gutter}px`
                  }"
                >
                  <div class="grid-item-content">
                    <ElFormItem :label="element.label" :prop="element.value">
                      <!-- 根据类型渲染不同的表单控件 -->
                      <component
                        :is="componentRap[constraintTypeMap[element.type.toUpperCase()]]"
                        v-bind="getFormItemProps(element)"
                      />
                    </ElFormItem>

                    <!-- 控制按钮 -->
                    <div class="grid-item-controls">
                      <!-- 调整宽度 -->
                      <div class="control-buttons">
                        <ElTooltip content="调整宽度" placement="top">
                          <ElSelect
                            v-model="element.span"
                            size="small"
                            style="width: 70px; margin-right: 8px"
                            @change="(val) => adjustElementSpan(element, val, rowIndex)"
                          >
                            <ElOption
                              v-for="i in gridConfig.columns"
                              :key="i"
                              :value="i"
                              :label="`${i}列`"
                            />
                          </ElSelect>
                        </ElTooltip>

                        <!-- 可见性设置 -->
                        <ElTooltip content="可见性设置" placement="top">
                          <ElPopover placement="bottom" :width="200" trigger="click">
                            <template #reference>
                              <ElButton type="primary" size="small" circle>
                                <Icon
                                  v-if="element.visibility === 'HIDDEN'"
                                  icon="mdi:hide-outline"
                                />
                                <Icon
                                  v-if="element.visibility === 'READONLY'"
                                  icon="mdi:eye-outline"
                                />
                                <Icon v-if="element.visibility === 'READWRITE'" icon="jam:write" />
                                <Icon v-if="!element.visibility" icon="uil:setting" />
                              </ElButton>
                            </template>
                            <div class="visibility-settings">
                              <p class="setting-title">可见性设置</p>
                              <ApiSelect
                                v-model="element.visibility"
                                @change="(val) => updateElementVisibility(element, val)"
                                v-bind="{
                                  multiple: false,
                                  component: ElSelect,
                                  apiConfig: {
                                    api: getDictByCodeApi,
                                    config: {
                                      label: 'label',
                                      value: 'value'
                                    }
                                  },
                                  params: {
                                    dictCode: constArr['visibility']
                                  },
                                  clearable: true
                                }"
                              />
                            </div>
                          </ElPopover>
                        </ElTooltip>
                      </div>

                      <!-- 删除按钮 -->
                      <ElButton
                        type="danger"
                        size="small"
                        circle
                        @click="removeElement(rowIndex, index)"
                      >
                        <ElIcon><Delete /></ElIcon>
                      </ElButton>
                    </div>
                  </div>
                </div>
              </template>
            </draggable>
          </div>
        </div>
      </div>
      <!-- 添加新行按钮 -->
      <div class="add-row mb-1">
        <ElButton type="primary" @click="addRow">
          <ElIcon><Plus /></ElIcon> 添加新行
        </ElButton>
      </div>
      <!-- 空状态提示 -->
      <div v-if="modelValue.every((row) => row.length === 0)" class="empty-target">
        拖拽左侧属性到此区域生成表单
      </div>
    </ElForm>
  </div>
</template>

<style scoped lang="less">
.draggable-panel {
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;

  .grid-layout {
    display: flex;
    width: 100%;
    height: 100%;
    flex-direction: column;
  }

  .grid-container {
    flex: 1;
    padding-right: 8px;
    margin-bottom: 12px;
  }

  .grid-row {
    display: flex;
    min-height: 80px;
    padding: 8px;
    margin-bottom: 12px;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 4px;
    flex-wrap: wrap;
  }

  .grid-row-items {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
  }

  .grid-item {
    margin-bottom: 12px;
    transition: all 0.3s;
  }

  .grid-item-content {
    position: relative;
    padding: 12px;
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .grid-item-controls {
    display: flex;
    padding-top: 8px;
    margin-top: 8px;
    border-top: 1px dashed #eee;
    justify-content: space-between;
    align-items: center;
  }

  .control-buttons {
    display: flex;
    align-items: center;
  }

  .visibility-settings {
    padding: 8px;

    .setting-title {
      margin-top: 0;
      margin-bottom: 8px;
      font-size: 14px;
      font-weight: bold;
    }
  }

  .add-to-row {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 80px;
  }

  .add-placeholder {
    display: flex;
    width: 100%;
    height: 100%;
    min-height: 80px;
    color: #999;
    background-color: rgba(0, 0, 0, 0.02);
    border: 1px dashed #ccc;
    border-radius: 4px;
    align-items: center;
    justify-content: center;
  }

  .add-row {
    display: flex;
    margin-top: 12px;
    justify-content: center;
  }

  .empty-target {
    padding: 40px 0;
    font-size: 14px;
    color: #999;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.02);
    border: 1px dashed #ccc;
    border-radius: 4px;
  }
}

/* 拖拽时的视觉效果 */
.sortable-chosen {
  background-color: #ebf5fb !important;
  opacity: 0.8;
}

.sortable-ghost {
  background: #c8ebfb !important;
  opacity: 0.5;
}
</style>
