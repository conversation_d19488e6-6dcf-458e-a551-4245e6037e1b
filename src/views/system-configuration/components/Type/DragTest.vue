<template>
  <div>
    <!-- 拖拽源 -->
    <draggable
      v-model="typeProperties"
      group="materials"
      item-key="value"
      :clone="handleClone"
      class="drop-area source"
    >
      <template #item="{ element }">
        <div class="material-item" v-if="!usedProperties.has(element.value)">
          {{ element.name }}
        </div>
      </template>
    </draggable>

    <!-- 放置目标 -->
    <draggable
      v-model="targetList"
      group="materials"
      item-key="id"
      class="drop-area target"
      @change="logChange"
    >
      <template #item="{ element }">
        <div class="material-item">
          {{ element.name }}
        </div>
      </template>
    </draggable>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import { reactive } from 'vue' // Vue 3

export default {
  components: { draggable },

  setup() {
    // Vue 3 示例
    const typeProperties = reactive([
      { value: 'name', name: '名称' },
      { value: 'age', name: '年龄' },
      { value: 'email', name: '邮箱' }
    ])

    const targetList = reactive([])
    const usedProperties = new Set()

    const handleClone = (original) => {
      return {
        ...JSON.parse(JSON.stringify(original)),
        id: Date.now() + Math.random() // 唯一ID
      }
    }

    const logChange = (evt) => {
      if (evt.added) {
        usedProperties.add(evt.added.element.value)
      }
    }

    return { typeProperties, targetList, usedProperties, handleClone, logChange }
  }
}
</script>

<style>
.drop-area {
  min-height: 100px;
  padding: 15px;
  margin: 20px 0;
  border: 2px dashed #ccc;
  border-radius: 4px;
}

.source {
  background-color: #f8f9fa;
}

.target {
  background-color: #e9f7ef;
}

.material-item {
  padding: 10px;
  margin: 8px 0;
  background: #e0e0e0;
  border-radius: 4px;
  cursor: grab;
  transition: all 0.3s;
}

.material-item:hover {
  background: #d0d0d0;
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
</style>
